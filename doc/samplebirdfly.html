<!doctype html>
<html lang="ja">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>SVG＋CSSで羽ばたくインコ</title>
  <style>
    :root {
      /* 配色はお好みで */
      --body: #9be37a;
      --belly: #c9f7b6;
      --beak: #f0b45e;
      --cheek: #ffdede;
      --wing: #7fdc60;
      --eye: #111;
      --stroke: #2a2a2a;
      --bg: #f7fbff;
    }

    html, body {
      height: 100%;
      margin: 0;
      background: radial-gradient(100% 120% at 50% 0%, var(--bg), #e8f2ff 70%);
      font-family: system-ui, -apple-system, Segoe UI, Roboto, "Hiragino Kaku Gothic ProN", Meiryo, sans-serif;
    }

    .stage {
      height: 100%;
      display: grid;
      place-items: center;
    }

    svg { width: min(78vmin, 520px); height: auto; overflow: visible; }

    /* 重要：SVGのtransform原点を要素の外接ボックスに */
    .use-fill-box { transform-box: fill-box; transform-origin: center; }

    /* 羽ばたき・体の上下・しっぽ・まばたきをCSSで制御 */
    @keyframes flap {
      0%   { transform: rotate(-8deg); }
      50%  { transform: rotate(32deg); }
      100% { transform: rotate(-8deg); }
    }
    @keyframes bob {
      0%,100% { transform: translateY(2px); }
      50%     { transform: translateY(-6px); }
    }
    @keyframes tail {
      0%,100% { transform: rotate(2deg); }
      50%     { transform: rotate(-6deg); }
    }
    @keyframes blink {
      0%, 6%, 100% { transform: scaleY(1); }
      3%             { transform: scaleY(0.1); }
      /* たまに二度瞬き */
      70%, 76%      { transform: scaleY(1); }
      73%           { transform: scaleY(0.1); }
    }
    @keyframes floatShadow {
      0%,100% { transform: translateX(-50%) scaleX(1); opacity: .18; }
      50%     { transform: translateX(-50%) scaleX(1.15); opacity: .10; }
    }

    /* 速度とディレイを少しずらして有機的に */
    .bird { animation: bob 1.2s ease-in-out infinite; }
    .wingL, .wingR { animation: flap .35s cubic-bezier(.39,.01,.64,1) infinite; }
    .wingR { animation-delay: -0.02s; }
    .tail  { animation: tail 1.2s ease-in-out infinite; }
    .eye   { animation: blink 5.4s linear infinite; transform-origin: 50% 50%; }
    .shadow { animation: floatShadow 1.2s ease-in-out infinite; }

    /* クリック/タップで一時停止 */
    .toggle { cursor: pointer; }
    .paused .anim { animation-play-state: paused !important; }

    /* アニメーション軽減設定 */
    @media (prefers-reduced-motion: reduce) {
      .anim { animation-duration: 4s; }
      .wingL, .wingR { animation: none; }
      .eye { animation: none; }
    }
  </style>
</head>
<body>
  <div class="stage">
    <!-- 影（HTML要素）にしてCSSでスケール -->
    <div id="shadow" class="shadow anim" style="position: absolute; bottom: 14vh; left: 50%; width: 160px; height: 24px; background: radial-gradient(50% 50%, rgba(0,0,0,.25), rgba(0,0,0,0)); border-radius: 50%; transform: translateX(-50%);"></div>

    <svg viewBox="0 0 200 200" role="img" aria-labelledby="title desc" class="toggle" id="scene">
      <title id="title">羽ばたくインコ</title>
      <desc id="desc">SVG と CSS アニメーションで描いた、空中で羽ばたく小さなインコ</desc>

      <!-- ふわっと浮かぶ鳥全体 -->
      <g class="bird anim use-fill-box">
        <!-- 右翼（奥） -->
        <g class="wingR anim use-fill-box" style="transform-origin: 112px 93px">
          <path d="M112 93 C 96 88, 86 78, 78 66 C 96 64, 112 70, 128 84 C 120 92, 114 94, 112 93 Z" fill="var(--wing)" stroke="var(--stroke)" stroke-width="2"/>
        </g>

        <!-- 体 -->
        <g class="body use-fill-box" style="transform-origin: 110px 112px">
          <ellipse cx="110" cy="112" rx="34" ry="40" fill="var(--body)" stroke="var(--stroke)" stroke-width="2"/>
          <ellipse cx="118" cy="126" rx="20" ry="18" fill="var(--belly)" opacity="0.9"/>
        </g>

        <!-- 尾羽 -->
        <g class="tail anim use-fill-box" style="transform-origin: 92px 140px">
          <path d="M92 140 L70 166 C76 168, 90 168, 104 164 Z" fill="var(--wing)" stroke="var(--stroke)" stroke-width="2"/>
        </g>

        <!-- 左翼（手前） -->
        <g class="wingL anim use-fill-box" style="transform-origin: 112px 93px">
          <path d="M112 93 C 98 90, 84 84, 74 74 C 94 70, 116 74, 136 88 C 126 94, 116 96, 112 93 Z" fill="var(--wing)" stroke="var(--stroke)" stroke-width="2"/>
        </g>

        <!-- 頭 -->
        <g class="head use-fill-box" style="transform-origin: 130px 92px">
          <circle cx="132" cy="92" r="18" fill="var(--body)" stroke="var(--stroke)" stroke-width="2"/>
          <circle cx="124" cy="96" r="6" fill="var(--cheek)"/>
          <g class="eye anim use-fill-box" style="transform-origin: 138px 90px">
            <ellipse cx="138" cy="90" rx="3.8" ry="3.8" fill="var(--eye)"/>
          </g>
          <path d="M148 96 L160 101 L148 106 Z" fill="var(--beak)" stroke="var(--stroke)" stroke-width="2"/>
        </g>

        <!-- 足（ちょいぷらぷら感）-->
        <g class="use-fill-box" style="transform-origin: 118px 144px">
          <path d="M116 144 q-4 8 4 8" fill="none" stroke="var(--stroke)" stroke-width="2"/>
          <path d="M124 144 q-2 8 6 8" fill="none" stroke="var(--stroke)" stroke-width="2"/>
        </g>
      </g>

      <!-- 装飾の雲 -->
      <g opacity=".5">
        <ellipse cx="36" cy="46" rx="18" ry="10" fill="#fff"/>
        <ellipse cx="52" cy="46" rx="18" ry="10" fill="#fff"/>
        <ellipse cx="44" cy="50" rx="20" ry="12" fill="#fff"/>
      </g>
      <g opacity=".5">
        <ellipse cx="172" cy="62" rx="18" ry="10" fill="#fff"/>
        <ellipse cx="188" cy="62" rx="18" ry="10" fill="#fff"/>
        <ellipse cx="180" cy="66" rx="20" ry="12" fill="#fff"/>
      </g>
    </svg>
  </div>

  <script>
    // クリック/タップで再生・一時停止を切り替え
    const scene = document.getElementById('scene');
    scene.addEventListener('click', () => {
      scene.classList.toggle('paused');
    });
  </script>
</body>
</html>