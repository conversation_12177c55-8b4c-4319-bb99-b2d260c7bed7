<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>アニメーションハムスター</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #f5e6d3, #ede0d0);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Arial', sans-serif;
        }
        
        .container {
            text-align: center;
        }
        
        .hamster-container {
            display: inline-block;
            filter: drop-shadow(0 8px 16px rgba(0,0,0,0.15));
        }
        
        h1 {
            color: #a0814d;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .bounce {
            animation: gentleBounce 4s ease-in-out infinite;
        }
        
        @keyframes gentleBounce {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐹 可愛いハムスター 🐹</h1>
        <div class="hamster-container bounce">
            <svg width="280" height="320" viewBox="0 0 280 320">
                <!-- 影 -->
                <ellipse cx="140" cy="300" rx="45" ry="12" fill="rgba(0,0,0,0.2)">
                    <animateTransform 
                        attributeName="transform" 
                        type="scale" 
                        values="1,1;1.05,1;1,1" 
                        dur="4s" 
                        repeatCount="indefinite"/>
                </ellipse>
                
                <!-- 足 -->
                <g id="feet">
                    <!-- 左後足 -->
                    <ellipse cx="115" cy="285" rx="8" ry="6" fill="#c49764" stroke="#a0814d" stroke-width="2">
                        <animateTransform 
                            attributeName="transform" 
                            type="rotate" 
                            values="0 115 285;-5 115 285;5 115 285;0 115 285" 
                            dur="2.5s" 
                            repeatCount="indefinite"/>
                    </ellipse>
                    
                    <!-- 右後足 -->
                    <ellipse cx="165" cy="285" rx="8" ry="6" fill="#c49764" stroke="#a0814d" stroke-width="2">
                        <animateTransform 
                            attributeName="transform" 
                            type="rotate" 
                            values="0 165 285;5 165 285;-5 165 285;0 165 285" 
                            dur="2.8s" 
                            repeatCount="indefinite"/>
                    </ellipse>
                    
                    <!-- 左前足 -->
                    <ellipse cx="105" cy="255" rx="6" ry="8" fill="#c49764" stroke="#a0814d" stroke-width="2">
                        <animateTransform 
                            attributeName="transform" 
                            type="translate" 
                            values="0,0;-2,1;1,-1;0,0" 
                            dur="1.8s" 
                            repeatCount="indefinite"/>
                    </ellipse>
                    
                    <!-- 右前足 -->
                    <ellipse cx="175" cy="255" rx="6" ry="8" fill="#c49764" stroke="#a0814d" stroke-width="2">
                        <animateTransform 
                            attributeName="transform" 
                            type="translate" 
                            values="0,0;2,1;-1,-1;0,0" 
                            dur="2.1s" 
                            repeatCount="indefinite"/>
                    </ellipse>
                </g>
                
                <!-- 体 -->
                <g id="body">
                    <!-- 胴体 -->
                    <ellipse cx="140" cy="220" rx="50" ry="65" fill="#ddb892" stroke="#a0814d" stroke-width="3"/>
                    
                    <!-- お腹の明るい部分 -->
                    <!-- <ellipse cx="140" cy="235" rx="35" ry="45" fill="#e6d4b7"/> -->
                    
                    <!-- 手 -->
                    <g id="hands" fill="#c49764" stroke="#a0814d" stroke-width="2">
                        <!-- 左手 -->
                        <g transform="translate(110, 200)">
                            <circle cx="0" cy="0" r="8">
                                <animateTransform 
                                    attributeName="transform" 
                                    type="rotate" 
                                    values="0;-15;10;0" 
                                    dur="3s" 
                                    repeatCount="indefinite"/>
                            </circle>
                            <!-- 小さな指 -->
                            <circle cx="-3" cy="-5" r="2" fill="#ddb892"/>
                            <circle cx="3" cy="-5" r="2" fill="#ddb892"/>
                        </g>
                        
                        <!-- 右手 -->
                        <g transform="translate(170, 200)">
                            <circle cx="0" cy="0" r="8">
                                <animateTransform 
                                    attributeName="transform" 
                                    type="rotate" 
                                    values="0;15;-10;0" 
                                    dur="3.5s" 
                                    repeatCount="indefinite"/>
                            </circle>
                            <!-- 小さな指 -->
                            <circle cx="-3" cy="-5" r="2" fill="#ddb892"/>
                            <circle cx="3" cy="-5" r="2" fill="#ddb892"/>
                        </g>
                    </g>
                </g>
                
                <!-- 頭 -->
                <g id="head">
                    <animateTransform 
                        attributeName="transform" 
                        type="rotate" 
                        values="0 140 140;-3 140 140;2 140 140;0 140 140;1 140 140;-2 140 140;0 140 140" 
                        dur="5s" 
                        repeatCount="indefinite"/>
                    
                    <!-- 頭の本体 -->
                    <ellipse cx="140" cy="140" rx="45" ry="42" fill="#ddb892" stroke="#a0814d" stroke-width="3"/>
                    
                    <!-- 頭の明るい部分 -->
                    <!-- <ellipse cx="140" cy="145" rx="30" ry="28" fill="#e6d4b7"/> -->
                    
                    <!-- 耳 -->
                    <g id="ears">
                        <!-- 左耳 -->
                        <circle cx="115" cy="115" r="18" fill="#ddb892" stroke="#a0814d" stroke-width="3">
                            <animateTransform 
                                attributeName="transform" 
                                type="rotate" 
                                values="0 115 115;-5 115 115;5 115 115;0 115 115" 
                                dur="4s" 
                                repeatCount="indefinite"/>
                        </circle>
                        <circle cx="115" cy="115" r="10" fill="#ddb892"/>
                        <ellipse cx="118" cy="112" rx="3" ry="2" fill="#c49764"/>
                        
                        <!-- 右耳 -->
                        <circle cx="165" cy="115" r="18" fill="#ddb892" stroke="#a0814d" stroke-width="3">
                            <animateTransform 
                                attributeName="transform" 
                                type="rotate" 
                                values="0 165 115;5 165 115;-5 165 115;0 165 115" 
                                dur="4.2s" 
                                repeatCount="indefinite"/>
                        </circle>
                        <circle cx="165" cy="115" r="10" fill="#ddb892"/>
                        <ellipse cx="162" cy="112" rx="3" ry="2" fill="#c49764"/>
                    </g>
                    
                    <!-- 目 -->
                    <g id="eyes">
                        <!-- 左目 -->
                        <circle cx="125" cy="135" r="8" fill="#2d1810">
                            <animate attributeName="cy" values="135;133;135" dur="3s" repeatCount="indefinite"/>
                        </circle>
                        <circle cx="127" cy="132" r="2" fill="white"/>
                        
                        <!-- 右目 -->
                        <circle cx="155" cy="135" r="8" fill="#2d1810">
                            <animate attributeName="cy" values="135;133;135" dur="3s" repeatCount="indefinite"/>
                        </circle>
                        <circle cx="157" cy="132" r="2" fill="white"/>
                        
                        <!-- まばたき -->
                        <g id="blink">
                            <ellipse cx="125" cy="135" rx="8" ry="0" fill="#ddb892" opacity="0">
                                <animate attributeName="ry" values="0;8;0" dur="0.2s" begin="0s;4s;7s;11s" fill="freeze"/>
                                <animate attributeName="opacity" values="0;1;0" dur="0.2s" begin="0s;4s;7s;11s" fill="freeze"/>
                            </ellipse>
                            <ellipse cx="155" cy="135" rx="8" ry="0" fill="#ddb892" opacity="0">
                                <animate attributeName="ry" values="0;8;0" dur="0.2s" begin="0s;4s;7s;11s" fill="freeze"/>
                                <animate attributeName="opacity" values="0;1;0" dur="0.2s" begin="0s;4s;7s;11s" fill="freeze"/>
                            </ellipse>
                        </g>
                    </g>
                    
                    <!-- 鼻 -->
                    <ellipse cx="140" cy="150" rx="2" ry="3" fill="#a0814d">
                        <animateTransform 
                            attributeName="transform" 
                            type="scale" 
                            values="1;1.2;1" 
                            dur="2s" 
                            repeatCount="indefinite"/>
                    </ellipse>
                    
                    <!-- 口 -->
                    <g id="mouth" fill="none" stroke="#a0814d" stroke-width="2" stroke-linecap="round">
                        <path d="M 140 155 Q 135 160 130 155"/>
                        <path d="M 140 155 Q 145 160 150 155"/>
                    </g>
                    
                    <!-- ひげ -->
                    <g id="whiskers" stroke="#a0814d" stroke-width="2" fill="none" stroke-linecap="round">
                        <!-- 左のひげ -->
                        <line x1="105" y1="145" x2="85" y2="140">
                            <animateTransform 
                                attributeName="transform" 
                                type="rotate" 
                                values="0 105 145;-2 105 145;2 105 145;0 105 145" 
                                dur="3s" 
                                repeatCount="indefinite"/>
                        </line>
                        <line x1="105" y1="155" x2="85" y2="155">
                            <animateTransform 
                                attributeName="transform" 
                                type="rotate" 
                                values="0 105 155;2 105 155;-2 105 155;0 105 155" 
                                dur="3.5s" 
                                repeatCount="indefinite"/>
                        </line>
                        
                        <!-- 右のひげ -->
                        <line x1="175" y1="145" x2="195" y2="140">
                            <animateTransform 
                                attributeName="transform" 
                                type="rotate" 
                                values="0 175 145;2 175 145;-2 175 145;0 175 145" 
                                dur="3s" 
                                repeatCount="indefinite"/>
                        </line>
                        <line x1="175" y1="155" x2="195" y2="155">
                            <animateTransform 
                                attributeName="transform" 
                                type="rotate" 
                                values="0 175 155;-2 175 155;2 175 155;0 175 155" 
                                dur="3.5s" 
                                repeatCount="indefinite"/>
                        </line>
                    </g>
                </g>
                
                <!-- 頬の丸み -->
                <g id="cheeks" opacity="0.6">
                    <circle cx="100" cy="150" r="12" fill="#ddb892">
                        <animateTransform 
                            attributeName="transform" 
                            type="scale" 
                            values="1;1.1;1" 
                            dur="4s" 
                            repeatCount="indefinite"/>
                    </circle>
                    <circle cx="180" cy="150" r="12" fill="#ddb892">
                        <animateTransform 
                            attributeName="transform" 
                            type="scale" 
                            values="1;1.1;1" 
                            dur="4.2s" 
                            repeatCount="indefinite"/>
                    </circle>
                </g>
            </svg>
        </div>
    </div>
</body>
</html>