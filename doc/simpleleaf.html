<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>アニメーションハムスター</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: 
                radial-gradient(circle at 20% 80%, rgba(255, 182, 193, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(144, 238, 144, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(250, 218, 221, 0.1) 0%, transparent 50%),
                linear-gradient(135deg, #f5e6d3, #ede0d0);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Arial', sans-serif;
            position: relative;
            overflow: hidden;
        }
        
        body::before {
            content: '';
            position: absolute;
            top: -100px;
            left: 0;
            width: 100%;
            height: calc(100% + 200px);
            background-image: 
                url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M30 20 Q 35 25 30 30 Q 25 25 20 30 Q 25 25 30 20 Z' fill='%23ffb6c1' opacity='0.3'/%3E%3C/svg%3E"),
                url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M20 10 Q 25 15 20 25 Q 15 20 10 25 Q 15 15 20 10 Z' fill='%23daa520' opacity='0.25'/%3E%3C/svg%3E");
            background-size: 200px 200px, 160px 160px;
            background-position: 0 0, 150px 0;
            animation: fallDown1 20s linear infinite;
            pointer-events: none;
        }
        
        body::after {
            content: '';
            position: absolute;
            top: -150px;
            left: 0;
            width: 100%;
            height: calc(100% + 300px);
            background-image: 
                url("data:image/svg+xml,%3Csvg width='50' height='50' viewBox='0 0 50 50' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M25 15 Q 30 20 25 25 Q 20 20 15 25 Q 20 20 25 15 Z' fill='%23ff69b4' opacity='0.2'/%3E%3C/svg%3E");
            background-size: 180px 180px;
            background-position: 200px 0;
            animation: fallDown2 30s linear infinite;
            pointer-events: none;
        }
        
        @keyframes fallDown1 {
            0% { 
                transform: translateY(-200px) translateX(0px) rotate(0deg);
            }
            25% { 
                transform: translateY(25vh) translateX(15px) rotate(45deg);
            }
            50% { 
                transform: translateY(50vh) translateX(-8px) rotate(90deg);
            }
            75% { 
                transform: translateY(75vh) translateX(10px) rotate(135deg);
            }
            100% { 
                transform: translateY(100vh) translateX(0px) rotate(180deg);
            }
        }
        
        @keyframes fallDown2 {
            0% { 
                transform: translateY(-250px) translateX(0px) rotate(0deg);
            }
            20% { 
                transform: translateY(20vh) translateX(-10px) rotate(36deg);
            }
            40% { 
                transform: translateY(40vh) translateX(18px) rotate(72deg);
            }
            60% { 
                transform: translateY(60vh) translateX(-12px) rotate(108deg);
            }
            80% { 
                transform: translateY(80vh) translateX(8px) rotate(144deg);
            }
            100% { 
                transform: translateY(110vh) translateX(0px) rotate(180deg);
            }
        }
        
        .container {
            text-align: center;
            position: relative;
            z-index: 2;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: -200px;
            left: -100px;
            width: calc(100% + 200px);
            height: calc(100% + 400px);
            background-image: 
                url("data:image/svg+xml,%3Csvg width='30' height='30' viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 8 Q 18 12 15 18 Q 12 14 8 18 Q 12 12 15 8 Z' fill='%23ffc0cb' opacity='0.25'/%3E%3C/svg%3E");
            background-size: 120px 120px;
            background-position: 80px 0;
            animation: sakuraFall 25s linear infinite;
            pointer-events: none;
            z-index: 1;
        }
        
        .container::after {
            content: '';
            position: absolute;
            top: -250px;
            left: -50px;
            width: calc(100% + 100px);
            height: calc(100% + 500px);
            background-image: 
                url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 5 Q 13 8 10 12 Q 7 9 4 12 Q 7 8 10 5 Z' fill='%23dda0dd' opacity='0.2'/%3E%3C/svg%3E");
            background-size: 100px 100px;
            background-position: 120px 0;
            animation: petalFall 35s linear infinite;
            pointer-events: none;
            z-index: 1;
        }
        
        @keyframes sakuraFall {
            0% { 
                transform: translateY(-300px) translateX(0px) rotate(0deg);
            }
            25% { 
                transform: translateY(25%) translateX(-20px) rotate(45deg);
            }
            50% { 
                transform: translateY(50%) translateX(15px) rotate(90deg);
            }
            75% { 
                transform: translateY(75%) translateX(-10px) rotate(135deg);
            }
            100% { 
                transform: translateY(120%) translateX(8px) rotate(180deg);
            }
        }
        
        @keyframes petalFall {
            0% { 
                transform: translateY(-400px) translateX(0px) rotate(0deg);
            }
            20% { 
                transform: translateY(20%) translateX(15px) rotate(36deg);
            }
            40% { 
                transform: translateY(40%) translateX(-12px) rotate(72deg);
            }
            60% { 
                transform: translateY(60%) translateX(20px) rotate(108deg);
            }
            80% { 
                transform: translateY(80%) translateX(-8px) rotate(144deg);
            }
            100% { 
                transform: translateY(130%) translateX(0px) rotate(180deg);
            }
        }
        
        .hamster-container {
            display: inline-block;
            filter: drop-shadow(0 8px 16px rgba(0,0,0,0.15));
            position: relative;
            z-index: 3;
        }
        
        h1 {
            color: #a0814d;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .bounce {
            animation: gentleBounce 4s ease-in-out infinite;
        }
        
        @keyframes gentleBounce {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌸 舞い散る桜と落ち葉 🍂</h1>
    </div>
</body>
</html>