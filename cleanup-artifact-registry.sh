#!/bin/bash

# Artifact Registry の清理スクリプト
PROJECT_ID="zhangshuzhencom"
REPOSITORY="zhangshuzhencom"
LOCATION="asia-northeast1"

echo "Artifact Registry の清理を開始します..."

# 現在のイメージ一覧を表示
echo "現在のイメージ一覧:"
gcloud artifacts docker images list ${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY} \
  --include-tags --format="table(IMAGE,TAGS,CREATE_TIME,UPDATE_TIME)"

# 清理ポリシーを適用
echo "清理ポリシーを適用中..."
gcloud artifacts repositories set-cleanup-policy ${REPOSITORY} \
  --location=${LOCATION} \
  --policy=artifact-registry-cleanup-policy.json \
  --project=${PROJECT_ID}

echo "清理ポリシーが設定されました。"
echo "注意: 実際の削除は次回のスケジュール実行時に行われます。"

# 手動で古いイメージを削除する場合（オプション）
echo "手動で古いイメージを削除しますか? (y/N)"
read -r response
if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    echo "30日以上古いイメージを削除中..."
    
    # 30日以上古いイメージを取得して削除
    OLD_IMAGES=$(gcloud artifacts docker images list ${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY} \
        --filter="createTime<$(date -d '30 days ago' --iso-8601)" \
        --format="value(IMAGE)" \
        --limit=50)
    
    if [ -n "$OLD_IMAGES" ]; then
        echo "削除対象のイメージ:"
        echo "$OLD_IMAGES"
        
        for image in $OLD_IMAGES; do
            echo "削除中: $image"
            gcloud artifacts docker images delete "$image" --quiet
        done
        
        echo "古いイメージの削除が完了しました。"
    else
        echo "削除対象の古いイメージはありませんでした。"
    fi
fi

echo "清理処理が完了しました。"


