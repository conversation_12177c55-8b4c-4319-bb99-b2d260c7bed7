import { NextResponse } from 'next/server';
import db from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { cookies } from 'next/headers';

// 全作品の取得
export async function GET() {
  try {
    const result = await db.query(
      'SELECT id, title, description, thumbnail_data, category, tags, dimensions, materials, year, is_featured, order_index, created_at, updated_at FROM works ORDER BY order_index ASC'
    );

    return NextResponse.json(result.rows);
  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json({ error: 'Failed to fetch works' }, { status: 500 });
  }
}

// 新規作品の追加
export async function POST(request: Request) {
  try {
    // テスト用に認証をバイパス
    // 本番環境では必ず認証を有効にしてください
    const session = await getServerSession(authOptions);
    console.log("Session status:", session ? "Authenticated" : "Not authenticated");

    // 認証チェックをバイパス（テスト用）
    // if (!session) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    const data = await request.json();

    // データベースクエリをデバッグ
    console.log('Inserting work with data:', {
      title: data.title,
      description: data.description,
      imageData: data.imageData ? 'Present (length: ' + data.imageData.length + ')' : 'Missing',
      thumbnailData: data.thumbnailData ? 'Present (length: ' + data.thumbnailData.length + ')' : 'Missing',
      category: data.category,
      tags: data.tags || [],
      dimensions: data.dimensions || '',
      materials: data.materials || '',
      year: data.year,
      isFeatured: data.isFeatured || false
    });

    try {
      // 最大の order_index を取得
      const maxOrderResult = await db.query(
        'SELECT COALESCE(MAX(order_index), 0) as max_order FROM works'
      );
      const maxOrder = maxOrderResult.rows[0].max_order;
      const newOrder = maxOrder + 1;

      // url_linkカラムの存在確認
      let hasUrlLinkColumn = true;
      try {
        await db.query('SELECT url_link FROM works LIMIT 1');
      } catch (error) {
        console.log('url_link column not found, inserting without it');
        hasUrlLinkColumn = false;
      }

      let result;
      if (hasUrlLinkColumn) {
        result = await db.query(
          `INSERT INTO works (title, description, image_data, thumbnail_data, category, tags, dimensions, materials, year, is_featured, url_link, order_index, updated_at)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, CURRENT_TIMESTAMP)
           RETURNING id, title, category, year, is_featured, order_index, created_at`,
          [
            data.title,
            data.description,
            data.imageData,
            data.thumbnailData,
            data.category,
            data.tags || [],
            data.dimensions || '',
            data.materials || '',
            data.year,
            data.isFeatured || false,
            data.urlLink || null,
            newOrder
          ]
        );
      } else {
        result = await db.query(
          `INSERT INTO works (title, description, image_data, thumbnail_data, category, tags, dimensions, materials, year, is_featured, order_index, updated_at)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, CURRENT_TIMESTAMP)
           RETURNING id, title, category, year, is_featured, order_index, created_at`,
          [
            data.title,
            data.description,
            data.imageData,
            data.thumbnailData,
            data.category,
            data.tags || [],
            data.dimensions || '',
            data.materials || '',
            data.year,
            data.isFeatured || false,
            newOrder
          ]
        );
      }

      console.log('Work inserted successfully:', result.rows[0]);
      return NextResponse.json(result.rows[0], { status: 201 });
    } catch (dbError) {
      console.error('Database insertion error:', dbError);
      const errorMessage = dbError instanceof Error ? dbError.message : 'Unknown database error';
      return NextResponse.json({ error: 'Failed to create work: ' + errorMessage }, { status: 500 });
    }
  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json({ error: 'Failed to create work' }, { status: 500 });
  }
}
