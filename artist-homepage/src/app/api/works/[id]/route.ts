import { NextResponse } from 'next/server';
import db from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// 特定の作品の取得
export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = params.id;
    const result = await db.query(
      'SELECT id, title, description, image_data, thumbnail_data, category, tags, dimensions, materials, year, is_featured, url_link, created_at, updated_at FROM works WHERE id = $1',
      [id]
    );

    if (result.rows.length === 0) {
      return NextResponse.json({ error: 'Work not found' }, { status: 404 });
    }

    return NextResponse.json(result.rows[0]);
  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json({ error: 'Failed to fetch work' }, { status: 500 });
  }
}

// 作品の更新
export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const id = params.id;
    const data = await request.json();

    // 画像データが提供されているかどうかを確認
    let updateQuery;
    let queryParams;

    if (data.imageData && data.thumbnailData) {
      updateQuery = `
        UPDATE works
        SET title = $1, description = $2, image_data = $3, thumbnail_data = $4,
            category = $5, tags = $6, dimensions = $7, materials = $8,
            year = $9, is_featured = $10, url_link = $11, updated_at = CURRENT_TIMESTAMP
        WHERE id = $12
        RETURNING id, title, category, year, is_featured, updated_at
      `;
      queryParams = [
        data.title,
        data.description,
        data.imageData,
        data.thumbnailData,
        data.category,
        data.tags || [],
        data.dimensions || '',
        data.materials || '',
        data.year,
        data.isFeatured || false,
        data.urlLink || null,
        id
      ];
    } else {
      // 画像データが提供されていない場合は、既存の画像データを保持
      updateQuery = `
        UPDATE works
        SET title = $1, description = $2, category = $3, tags = $4,
            dimensions = $5, materials = $6, year = $7, is_featured = $8,
            url_link = $9, updated_at = CURRENT_TIMESTAMP
        WHERE id = $10
        RETURNING id, title, category, year, is_featured, updated_at
      `;
      queryParams = [
        data.title,
        data.description,
        data.category,
        data.tags || [],
        data.dimensions || '',
        data.materials || '',
        data.year,
        data.isFeatured || false,
        data.urlLink || null,
        id
      ];
    }

    const result = await db.query(updateQuery, queryParams);

    if (result.rows.length === 0) {
      return NextResponse.json({ error: 'Work not found' }, { status: 404 });
    }

    return NextResponse.json(result.rows[0]);
  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json({ error: 'Failed to update work' }, { status: 500 });
  }
}

// 作品の削除
export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  console.log('DELETE request received for work ID:', params.id);

  try {
    const session = await getServerSession(authOptions);
    console.log('Session check:', session ? 'Authenticated' : 'Unauthenticated');

    if (!session) {
      console.log('Unauthorized delete attempt');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const id = params.id;
    console.log('Executing DELETE query for work ID:', id);

    const result = await db.query('DELETE FROM works WHERE id = $1 RETURNING id', [id]);
    console.log('DELETE query result:', result.rows);

    if (result.rows.length === 0) {
      console.log('Work not found for deletion');
      return NextResponse.json({ error: 'Work not found' }, { status: 404 });
    }

    console.log('Work deleted successfully');
    return NextResponse.json({ message: 'Work deleted successfully' });
  } catch (error) {
    console.error('Database error during deletion:', error);
    return NextResponse.json({ error: 'Failed to delete work' }, { status: 500 });
  }
}
