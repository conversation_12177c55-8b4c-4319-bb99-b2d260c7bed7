import Link from 'next/link'
import HeroSlider from '@/components/HeroSlider'
import FeaturedWorks from '@/components/FeaturedWorks'
import AnimatedBirds from '@/components/AnimatedBirds'
import LatestNews from '@/components/LatestNews'
import ScrollFadeIn from '@/components/ScrollFadeIn'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: process.env.NEXT_PUBLIC_SITE_NAME || 'Zhang Shuzhen com',
  description: `${process.env.NEXT_PUBLIC_ARTIST_NAME} - ${process.env.NEXT_PUBLIC_ARTIST_PROFESSION} - 公式ウェブサイト`,
}

export default function Home() {
  return (
    <div className="flex min-h-screen flex-col">
      {/* Hero Section with Slider */}
      <HeroSlider />

      {/* Artist Statement - New Section */}
      <ScrollFadeIn>
        <section className="py-20 bg-primary-50">
          <div className="container mx-auto px-4 max-w-4xl text-center relative overflow-hidden">
            <h1 className="sr-only"><PERSON> | 张 淑桢 - 現代日本画アーティスト公式サイト</h1>
            <AnimatedBirds />
            <h2 className="text-3xl md:text-4xl font-serif text-primary-800 mb-6 font-medium">
              鳥の声、風のかたち、土の記憶
            </h2>
            <p className="text-lg md:text-xl text-primary-700 mb-8 leading-relaxed">
              誰の記憶にもない自然といにしえが語り合う世界を描く現代日本画アーティスト
            </p>
            <div className="w-24 h-1 bg-primary-400 mx-auto"></div>
          </div>
        </section>
      </ScrollFadeIn>

      {/* Featured Works Section with New Design */}
      <ScrollFadeIn delay={200}>
        <section className="py-16 md:py-24">
          <div className="container mx-auto px-4">
            <h2 className="text-2xl md:text-3xl font-display font-bold text-primary-800 mb-12 text-center">
              <span className="inline-block border-b-2 border-primary-400 pb-2">注目作品</span>
            </h2>
            <FeaturedWorks />
            <div className="text-center mt-12">
              <Link
                href="/works"
                className="inline-block btn-seasonal py-3 px-8 rounded-md hover:shadow-lg duration-300"
              >
                すべての作品を見る
              </Link>
            </div>
          </div>
        </section>
      </ScrollFadeIn>

      {/* Latest News Section with Improved Design */}
      <ScrollFadeIn delay={400}>
        <section className="py-16 md:py-24 bg-gradient-to-b from-white to-primary-50">
          <div className="container mx-auto px-4">
            <h2 className="text-2xl md:text-3xl font-display font-bold text-primary-800 mb-12 text-center">
              <span className="inline-block border-b-2 border-primary-400 pb-2">最新ニュース</span>
            </h2>
            <LatestNews />
            <div className="text-center mt-8">
              <Link
                href="/news"
                className="inline-block link-seasonal font-medium transition-colors"
              >
                すべてのニュースを見る →
              </Link>
            </div>
          </div>
        </section>
      </ScrollFadeIn>

      {/* Call to Action with Enhanced Visual Appeal */}
      <section className="py-20 bg-primary-600 text-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute w-64 h-64 rounded-full bg-white/30 -top-20 -left-20 blur-3xl"></div>
          <div className="absolute w-64 h-64 rounded-full bg-white/20 bottom-10 right-10 blur-3xl"></div>
        </div>
        <div className="container mx-auto px-4 text-center relative z-10">
          <h2 className="text-3xl md:text-4xl font-serif font-bold mb-6">作品制作依頼・展示会のご相談</h2>
          <p className="text-xl mb-10 max-w-2xl mx-auto leading-relaxed">
            作品制作のご依頼や展示会に関するお問い合わせは、お気軽にご連絡ください。
          </p>
          <Link
            href="/contact"
            className="inline-block bg-white text-primary-600 py-3 px-10 rounded-md hover:bg-gray-100 transition-all duration-300 hover:shadow-xl transform hover:-translate-y-1 font-medium"
          >
            お問い合わせ
          </Link>
        </div>
      </section>
    </div>
  )
}
