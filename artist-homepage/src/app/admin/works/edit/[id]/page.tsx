'use client';

export const dynamic = 'force-dynamic';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';
import Image from 'next/image';
import { toast } from 'react-hot-toast';
import AdminLayout from '@/components/AdminLayout';
import ImageRotationControl from '@/components/admin/ImageRotationControl';

interface Work {
  id: number;
  title: string;
  description: string;
  image_data: string;
  thumbnail_data: string;
  category: string;
  tags: string[];
  dimensions: string;
  materials: string;
  year: number;
  is_featured: boolean;
  url_link?: string;
}

export default function EditWorkPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const id = params?.id as string;

  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('');
  const [tags, setTags] = useState('');
  const [dimensions, setDimensions] = useState('');
  const [materials, setMaterials] = useState('');
  const [year, setYear] = useState<number>(new Date().getFullYear());
  const [isFeatured, setIsFeatured] = useState(false);
  const [urlLink, setUrlLink] = useState('');

  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [imageData, setImageData] = useState<string | null>(null);
  const [thumbnailData, setThumbnailData] = useState<string | null>(null);
  const [rotationAngle, setRotationAngle] = useState<number>(0);

  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');

  // 作品データの取得
  useEffect(() => {
    async function fetchWork() {
      try {
        const res = await fetch(`/api/works/${id}`);
        if (!res.ok) throw new Error('Failed to fetch work');

        const work: Work = await res.json();

        setTitle(work.title);
        setDescription(work.description || '');
        setCategory(work.category);
        setTags(work.tags ? work.tags.join(', ') : '');
        setDimensions(work.dimensions || '');
        setMaterials(work.materials || '');
        setYear(work.year);
        setIsFeatured(work.is_featured);
        setUrlLink(work.url_link || '');

        setImagePreview(work.image_data);
        setImageData(work.image_data);
        setThumbnailData(work.thumbnail_data);
      } catch (error) {
        console.error('Fetch error:', error);
        setError('作品の取得に失敗しました。');
      } finally {
        setLoading(false);
      }
    }

    if (status === 'authenticated' && id) {
      fetchWork();
    }
  }, [status, id]);

  // 画像ファイルが選択されたときの処理
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // ファイルタイプの検証
    if (!file.type.startsWith('image/')) {
      setError('画像ファイルを選択してください。');
      return;
    }

    setImageFile(file);

    // プレビュー用のURL生成
    const reader = new FileReader();
    reader.onloadend = () => {
      setImagePreview(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  // 画像のアップロードと処理
  const uploadImage = async () => {
    if (!imageFile) return null;

    const formData = new FormData();
    formData.append('file', imageFile);
    // 回転角度を追加
    formData.append('rotate', rotationAngle.toString());

    try {
      const res = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!res.ok) throw new Error('Failed to upload image');

      const data = await res.json();
      setImageData(data.imageData);
      setThumbnailData(data.thumbnailData);
      return data;
    } catch (error) {
      console.error('Upload error:', error);
      setError('画像のアップロードに失敗しました。');
      return null;
    }
  };

  // 画像の回転角度を変更する処理
  const handleRotate = (angle: number) => {
    setRotationAngle(angle);

    // プレビュー画像にスタイルで回転を適用（実際の画像処理はアップロード時に行う）
    if (imagePreview) {
      const img = document.getElementById('image-preview') as HTMLImageElement;
      if (img) {
        img.style.transform = `rotate(${angle}deg)`;
      }
    }
  };

  // フォーム送信処理
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSubmitting(true);

    try {
      // 必須項目の検証
      if (!title || !category || !year) {
        setError('タイトル、カテゴリ、制作年は必須です。');
        setSubmitting(false);
        return;
      }

      let uploadResult = null;

      // 新しい画像がアップロードされた場合
      if (imageFile) {
        uploadResult = await uploadImage();
        if (!uploadResult) {
          setSubmitting(false);
          return;
        }
      }

      // 作品データの送信
      const workData = {
        title,
        description,
        category,
        tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        dimensions,
        materials,
        year,
        isFeatured,
        urlLink: urlLink.trim() || null,
      };

      // 新しい画像がアップロードされた場合は、画像データも含める
      if (uploadResult) {
        Object.assign(workData, {
          imageData: uploadResult.imageData,
          thumbnailData: uploadResult.thumbnailData,
        });
      }

      const res = await fetch(`/api/works/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(workData),
      });

      if (!res.ok) throw new Error('Failed to update work');

      // 成功したら作品一覧ページにリダイレクト
      router.push('/admin/works');
    } catch (error) {
      console.error('Submission error:', error);
      setError('作品の更新に失敗しました。');
    } finally {
      setSubmitting(false);
    }
  };

  // 作品削除処理
  const handleDelete = async () => {
    // 削除確認
    if (!window.confirm(`この作品を削除してもよろしいですか？この操作は元に戻せません。`)) {
      return;
    }

    setError('');
    setSubmitting(true);

    try {
      const res = await fetch(`/api/works/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!res.ok) {
        const errorData = await res.json();
        console.error('Delete error response:', errorData);
        throw new Error(`Failed to delete work: ${errorData.error || res.statusText}`);
      }

      // 成功メッセージを表示
      toast.success('作品を削除しました');

      // 作品一覧ページにリダイレクト
      router.push('/admin/works');
    } catch (error) {
      console.error('Delete error:', error);
      setError('作品の削除に失敗しました。');
      toast.error('作品の削除に失敗しました');
      setSubmitting(false);
    }
  };

  if (status === 'unauthenticated') {
    router.push('/admin/login');
    return null;
  }

  if (status === 'loading' || loading) {
    return (
      <AdminLayout>
        <div className="text-center py-10">Loading...</div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="mb-8">
        <h1 className="text-3xl font-bold">作品の編集</h1>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 左側: 基本情報 */}
          <div className="space-y-4">
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                タイトル *
              </label>
              <input
                id="title"
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                説明
              </label>
              <textarea
                id="description"
                rows={4}
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>

            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                カテゴリ *
              </label>
              <select
                id="category"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                required
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="">カテゴリを選択</option>
                <option value="painting">絵画</option>
                <option value="sculpture">彫刻</option>
                <option value="photography">写真</option>
                <option value="digital">デジタル</option>
                <option value="other">その他</option>
              </select>
            </div>

            <div>
              <label htmlFor="tags" className="block text-sm font-medium text-gray-700">
                タグ (カンマ区切り)
              </label>
              <input
                id="tags"
                type="text"
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                placeholder="例: 風景, 自然, 抽象"
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>

            <div>
              <label htmlFor="urlLink" className="block text-sm font-medium text-gray-700">
                関連リンク (URL)
              </label>
              <input
                id="urlLink"
                type="url"
                value={urlLink}
                onChange={(e) => setUrlLink(e.target.value)}
                placeholder="例: https://example.com/exhibition"
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              <p className="mt-1 text-sm text-gray-500">
                作品に関連する展覧会やイベントのURLを入力してください（任意）
              </p>
            </div>
          </div>

          {/* 右側: 詳細情報と画像 */}
          <div className="space-y-4">
            <div>
              <label htmlFor="dimensions" className="block text-sm font-medium text-gray-700">
                サイズ
              </label>
              <input
                id="dimensions"
                type="text"
                value={dimensions}
                onChange={(e) => setDimensions(e.target.value)}
                placeholder="例: 60 x 80 cm, F10 (53.0 x 45.5 cm), P8 (45.5 x 33.3 cm), M20 (72.7 x 50.0 cm)"
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>

            <div>
              <label htmlFor="materials" className="block text-sm font-medium text-gray-700">
                素材
              </label>
              <input
                id="materials"
                type="text"
                value={materials}
                onChange={(e) => setMaterials(e.target.value)}
                placeholder="例: 岩絵具・和紙"
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>

            <div>
              <label htmlFor="year" className="block text-sm font-medium text-gray-700">
                制作年 *
              </label>
              <input
                id="year"
                type="number"
                value={year}
                onChange={(e) => setYear(parseInt(e.target.value))}
                required
                min="1900"
                max={new Date().getFullYear()}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>

            <div className="flex items-center">
              <input
                id="isFeatured"
                type="checkbox"
                checked={isFeatured}
                onChange={(e) => setIsFeatured(e.target.checked)}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label htmlFor="isFeatured" className="ml-2 block text-sm text-gray-700">
                注目作品として表示
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">画像</label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                <div className="space-y-1 text-center">
                  {imagePreview ? (
                    <div className="space-y-4">
                      <div className="relative h-40 w-full mb-4">
                        <Image
                          id="image-preview"
                          src={imagePreview}
                          alt="Preview"
                          fill
                          style={{
                            objectFit: 'contain',
                            transform: `rotate(${rotationAngle}deg)`,
                            transition: 'transform 0.3s ease'
                          }}
                          className="rounded"
                        />
                      </div>

                      {/* 回転コントロールを追加 */}
                      <ImageRotationControl
                        onRotate={handleRotate}
                        currentAngle={rotationAngle}
                      />
                    </div>
                  ) : (
                    <svg
                      className="mx-auto h-12 w-12 text-gray-400"
                      stroke="currentColor"
                      fill="none"
                      viewBox="0 0 48 48"
                      aria-hidden="true"
                    >
                      <path
                        d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                        strokeWidth={2}
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  )}
                  <div className="flex text-sm text-gray-600">
                    <label
                      htmlFor="image-upload"
                      className="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"
                    >
                      <span>画像を変更</span>
                      <input
                        id="image-upload"
                        name="image-upload"
                        type="file"
                        accept="image/*"
                        className="sr-only"
                        onChange={handleImageChange}
                      />
                    </label>
                    <p className="pl-1">またはドラッグ＆ドロップ</p>
                  </div>
                  <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-between items-center">
          <button
            type="button"
            onClick={handleDelete}
            disabled={submitting}
            className="px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
          >
            削除
          </button>

          <div className="flex space-x-3">
            <button
              type="button"
              onClick={() => router.push('/admin/works')}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              キャンセル
            </button>
            <button
              type="submit"
              disabled={submitting}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              {submitting ? '保存中...' : '保存'}
            </button>
          </div>
        </div>
      </form>
    </AdminLayout>
  );
}
