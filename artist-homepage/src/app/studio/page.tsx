'use client';

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

const AnimatedCharacterDynamic = dynamic(() => import('@/components/AnimatedCharacter'), { ssr: false });
const FallingLeavesBackgroundDynamic = dynamic(() => import('@/components/FallingLeavesBackground'), { ssr: false });

interface StudioItem {
  id: number;
  title: string;
  description: string;
  image_data: string;
  order_index: number;
  is_active: boolean;
}

export default function StudioPage() {
  const [studioItems, setStudioItems] = useState<StudioItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    async function fetchStudioItems() {
      try {
        const res = await fetch('/api/admin/studio');
        if (!res.ok) throw new Error('Failed to fetch studio items');

        const data = await res.json();
        console.log('Studio items from API:', data);
        setStudioItems(data);
      } catch (error) {
        console.error('Fetch error:', error);
        setError('スタジオ情報の取得に失敗しました。');
      } finally {
        setLoading(false);
      }
    }

    fetchStudioItems();
  }, []);

  return (
    <div className="studio-page min-h-screen">
      {/* 季節感のある背景グラデーション */}
      <div className="studio-background-gradient" />

      {/* 落ち葉・桜のアニメーション背景 */}
      <FallingLeavesBackgroundDynamic theme="mixed" intensity="medium" />

      <div className="container mx-auto px-4 py-8 relative z-10">
        <h1 className="text-3xl font-bold mb-8 flex items-center gap-4">
          スタジオ
          <span className="inline-block">
            <AnimatedCharacterDynamic type="hamster" size="md" />
          </span>
        </h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {loading ? (
        <div className="text-center py-10">
          <p>読み込み中...</p>
        </div>
      ) : studioItems.length === 0 ? (
        <div className="prose max-w-none">
          <p>現在、スタジオ情報はありません。</p>
        </div>
      ) : (
        <div className="space-y-16">
          {studioItems.map((item) => (
            <div key={item.id} className="mb-12">
              <h2 className="text-2xl font-bold mb-4">{item.title}</h2>
              <div className="mb-4">
                <img
                  src={item.image_data}
                  alt={item.title}
                  className="w-full max-w-2xl h-auto rounded-lg shadow-md"
                />
              </div>
              <div className="prose max-w-none">
                <p>{item.description}</p>
              </div>
            </div>
          ))}
        </div>
      )}
      </div>
    </div>
  );
}
