import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'スタジオ | <PERSON> Shuzhen',
  description: '<PERSON>（張淑桢）のアトリエ・スタジオの様子をご紹介します。現代日本画制作の現場、作業風景、制作過程をご覧いただけます。',
  openGraph: {
    title: 'スタジオ | <PERSON>',
    description: '<PERSON>（張淑桢）のアトリエ・スタジオの様子をご紹介します。現代日本画制作の現場、作業風景、制作過程をご覧いただけます。',
    url: 'https://zhangshuzhen.com/studio',
    images: [
      {
        url: '/images/studio-og-image.jpg',
        width: 1200,
        height: 630,
        alt: '<PERSON> スタジオ',
      }
    ],
    type: 'website',
  },
  alternates: {
    canonical: 'https://zhangshuzhen.com/studio',
  },
};

export default function StudioLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="studio-page">
      {children}
    </div>
  )
}
