'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

interface LightboxProps {
  isOpen: boolean;
  onClose: () => void;
  imageSrc: string;
  imageAlt: string;
  title?: string;
  description?: string;
}

export default function Lightbox({ 
  isOpen, 
  onClose, 
  imageSrc, 
  imageAlt, 
  title, 
  description 
}: LightboxProps) {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.body.style.overflow = 'unset';
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90 p-4"
      onClick={onClose}
    >
      <div 
        className="relative max-w-4xl max-h-full bg-white rounded-lg shadow-2xl overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 閉じるボタン */}
        <button
          className="absolute top-4 right-4 z-10 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all duration-200"
          onClick={onClose}
          aria-label="閉じる"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* 画像 */}
        <div className="relative">
          <Image
            src={imageSrc}
            alt={imageAlt}
            width={800}
            height={600}
            className="w-full h-auto max-h-[70vh] object-contain"
            priority
          />
        </div>

        {/* 画像情報 */}
        {(title || description) && (
          <div className="p-6 bg-white">
            {title && (
              <h3 className="text-xl font-bold text-gray-900 mb-2">{title}</h3>
            )}
            {description && (
              <p className="text-gray-600 leading-relaxed">{description}</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
