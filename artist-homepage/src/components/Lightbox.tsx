'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

interface LightboxProps {
  isOpen: boolean;
  onClose: () => void;
  imageSrc: string;
  imageAlt: string;
  title?: string;
  description?: string;
  clickPosition?: { x: number; y: number };
  thumbnailRect?: DOMRect;
}

export default function Lightbox({
  isOpen,
  onClose,
  imageSrc,
  imageAlt,
  title,
  description,
  clickPosition,
  thumbnailRect
}: LightboxProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);
  const [animationStyle, setAnimationStyle] = useState<React.CSSProperties>({});

  useEffect(() => {
    if (isOpen) {
      setShouldRender(true);

      // 初期状態を設定（サムネイルの位置とサイズ）
      if (clickPosition && thumbnailRect) {
        setAnimationStyle({
          position: 'fixed' as const,
          left: thumbnailRect.left,
          top: thumbnailRect.top,
          width: thumbnailRect.width,
          height: thumbnailRect.height,
          transform: 'scale(1)',
          transformOrigin: 'center',
          transition: 'none',
          zIndex: 60
        });
      } else {
        // フォールバック: 中央から小さく開始
        setAnimationStyle({
          position: 'fixed' as const,
          left: '50%',
          top: '50%',
          width: '200px',
          height: '150px',
          transform: 'translate(-50%, -50%) scale(0.1)',
          transformOrigin: 'center',
          transition: 'none',
          zIndex: 60
        });
      }

      // 少し遅延させてアニメーションを開始
      setTimeout(() => {
        setIsVisible(true);
        // 最終的な位置とサイズに移動（画面中央、より大きく表示）
        setAnimationStyle(prev => ({
          ...prev,
          left: '50%',
          top: '50%',
          width: 'auto',
          height: 'auto',
          maxWidth: '95vw',
          maxHeight: '95vh',
          minWidth: '60vw',  // 最小幅を設定
          transform: 'translate(-50%, -50%) scale(1)',
          transition: 'all 600ms cubic-bezier(0.25, 0.46, 0.45, 0.94)'
        }));
      }, 50);

      document.body.style.overflow = 'hidden';
    } else {
      setIsVisible(false);
      // 閉じるアニメーション
      if (clickPosition && thumbnailRect) {
        setAnimationStyle(prev => ({
          ...prev,
          left: thumbnailRect.left,
          top: thumbnailRect.top,
          width: thumbnailRect.width,
          height: thumbnailRect.height,
          transform: 'scale(0.1)',
          transition: 'all 400ms cubic-bezier(0.55, 0.055, 0.675, 0.19)'
        }));
      } else {
        setAnimationStyle(prev => ({
          ...prev,
          transform: 'translate(-50%, -50%) scale(0.1)',
          transition: 'all 400ms cubic-bezier(0.55, 0.055, 0.675, 0.19)'
        }));
      }
      // アニメーション完了後にDOMから削除
      setTimeout(() => setShouldRender(false), 600);
      document.body.style.overflow = 'unset';
    }

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.body.style.overflow = 'unset';
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!shouldRender) return null;

  return (
    <div
      className={`fixed inset-0 z-50 bg-black transition-all duration-[600ms] ease-out ${
        isVisible ? 'bg-opacity-90' : 'bg-opacity-0'
      }`}
      onClick={onClose}
    >
      <div
        className="relative bg-white rounded-lg shadow-2xl overflow-hidden"
        style={animationStyle}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 閉じるボタン */}
        <button
          className="absolute top-4 right-4 z-10 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all duration-[600ms]"
          onClick={onClose}
          aria-label="閉じる"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* 画像 */}
        <div className="relative">
          <Image
            src={imageSrc}
            alt={imageAlt}
            width={1200}
            height={900}
            className="w-full h-auto max-h-[90vh] object-contain"
            priority
          />
        </div>

        {/* 画像情報 */}
        {(title || description) && (
          <div className="p-6 bg-white">
            {title && (
              <h3 className="text-xl font-bold text-gray-900 mb-2">{title}</h3>
            )}
            {description && (
              <p className="text-gray-600 leading-relaxed">{description}</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
