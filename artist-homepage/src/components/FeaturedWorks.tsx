'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import Link from 'next/link';
import AspectRatioImage from './AspectRatioImage';
import { SeasonalLoadingSpinner } from './LoadingSpinner';

interface Work {
  id: number;
  title: string;
  description: string;
  thumbnail_data: string;
  category: string;
  year: number;
  order_index?: number;
}

export default function FeaturedWorks() {
  const [works, setWorks] = useState<Work[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [lastUpdate, setLastUpdate] = useState<string>('');
  const lastFetchTimeRef = useRef<number>(0);

  // バランスを考慮した再取得間隔（2分 = 120秒）
  const REFETCH_INTERVAL = 120 * 1000;

  const fetchFeaturedWorks = useCallback(async (force = false) => {
    // 前回の取得からREFETCH_INTERVAL以内の場合、強制フラグがなければスキップ
    const now = Date.now();
    if (!force && now - lastFetchTimeRef.current < REFETCH_INTERVAL) {
      return;
    }

    try {
      setError('');
      const timestamp = now;
      lastFetchTimeRef.current = timestamp;

      // キャッシュを無効化するためのランダムクエリパラメータとキャッシュヘッダーを追加
      console.log('Fetching featured works from API...');
      const res = await fetch(`/api/works/featured?t=${timestamp}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      if (!res.ok) throw new Error('Failed to fetch featured works');

      const data = await res.json();
      const receivedWorks = data.works || data; // APIの応答形式に対応

      console.log(`注目作品: データ更新(${new Date().toLocaleTimeString()}):`,
        receivedWorks.length, '件',
        data.timestamp ? `最終更新: ${data.timestamp}` : '');

      if (data.timestamp) {
        setLastUpdate(data.timestamp);
      }

      console.log('Featured works from API:', {
        count: receivedWorks.length,
        works: receivedWorks.map((w: Work) => ({
          id: w.id,
          title: w.title,
          order_index: w.order_index,
          has_thumbnail: !!w.thumbnail_data,
          thumbnail_length: w.thumbnail_data ? w.thumbnail_data.length : 0,
          has_required_fields: !!(w.id && w.title && w.description && w.thumbnail_data && w.category && w.year)
        }))
      });

      setWorks(receivedWorks);
    } catch (error) {
      console.error('Fetch error:', error);
      setError('注目作品の取得に失敗しました。');
    } finally {
      setLoading(false);
    }
  }, []);

  // 初回読み込みと定期的な更新
  useEffect(() => {
    // 初回読み込み
    fetchFeaturedWorks(true);

    // 定期的な更新（バランスを考慮した間隔）
    const refreshInterval = setInterval(() => {
      fetchFeaturedWorks(true);
    }, REFETCH_INTERVAL);

    return () => clearInterval(refreshInterval);
  }, [fetchFeaturedWorks]);

  if (loading) {
    return (
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8">注目の作品</h2>
          <div className="flex justify-center py-10">
            <SeasonalLoadingSpinner size="lg" text="作品を読み込み中..." />
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return null; // エラーの場合はコンポーネントを表示しない
  }

  if (works.length === 0) {
    return null; // 注目作品がない場合はコンポーネントを表示しない
  }

  return (
    <section className="py-12 bg-seasonal-gradient">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold mb-8 text-center text-fade-in">注目の作品</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {works.map((work, index) => (
            <Link key={work.id} href={`/works/${work.id}`} className="block">
              <div
                className="bg-white rounded-lg shadow-md overflow-hidden art-card card-float"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="overflow-hidden relative image-zoom">
                  <AspectRatioImage
                    src={work.thumbnail_data}
                    alt={work.title}
                    containerClassName="rounded-t-lg art-card-image"
                  />
                  <div className="art-card-overlay"></div>
                </div>
                <div className="p-4">
                  <h3 className="text-xl font-semibold mb-2 link-seasonal">{work.title}</h3>
                  <p className="text-gray-600 mb-2">{work.category}</p>
                  <p className="text-gray-500">{work.year}年</p>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}