'use client';

import { useEffect, useState } from 'react';

interface FallingLeavesBackgroundProps {
  theme?: 'autumn' | 'spring' | 'mixed';
  intensity?: 'light' | 'medium' | 'heavy';
  className?: string;
}

export default function FallingLeavesBackground({ 
  theme = 'mixed', 
  intensity = 'medium',
  className = '' 
}: FallingLeavesBackgroundProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // コンポーネントがマウントされた後にアニメーションを開始
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  const getThemeColors = () => {
    switch (theme) {
      case 'autumn':
        return {
          leaf1: '%23ff6b35', // オレンジ
          leaf2: '%23daa520', // ゴールデンロッド
          leaf3: '%23cd853f', // ペルー
          petal1: '%23d2691e', // チョコレート
          petal2: '%23b8860b'  // ダークゴールデンロッド
        };
      case 'spring':
        return {
          leaf1: '%23ffb6c1', // ライトピンク
          leaf2: '%23ffc0cb', // ピンク
          leaf3: '%23ff69b4', // ホットピンク
          petal1: '%23dda0dd', // プラム
          petal2: '%23da70d6'  // オーキッド
        };
      default: // mixed
        return {
          leaf1: '%23ffb6c1', // ライトピンク
          leaf2: '%23daa520', // ゴールデンロッド
          leaf3: '%23ff69b4', // ホットピンク
          petal1: '%23ffc0cb', // ピンク
          petal2: '%23dda0dd'  // プラム
        };
    }
  };

  const colors = getThemeColors();

  const getIntensitySettings = () => {
    switch (intensity) {
      case 'light':
        return {
          opacity1: '0.15',
          opacity2: '0.12',
          opacity3: '0.1',
          size1: '150px',
          size2: '120px',
          size3: '100px'
        };
      case 'heavy':
        return {
          opacity1: '0.35',
          opacity2: '0.3',
          opacity3: '0.25',
          size1: '250px',
          size2: '200px',
          size3: '180px'
        };
      default: // medium
        return {
          opacity1: '0.25',
          opacity2: '0.2',
          opacity3: '0.18',
          size1: '200px',
          size2: '160px',
          size3: '140px'
        };
    }
  };

  const settings = getIntensitySettings();

  if (!isVisible) return null;

  return (
    <div className={`falling-leaves-background ${className}`}>
      {/* 背景レイヤー1 - 大きな落ち葉 */}
      <div 
        className="falling-layer falling-layer-1"
        style={{
          backgroundImage: `
            url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M30 20 Q 35 25 30 30 Q 25 25 20 30 Q 25 25 30 20 Z' fill='${colors.leaf1}' opacity='${settings.opacity1}'/%3E%3C/svg%3E"),
            url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M20 10 Q 25 15 20 25 Q 15 20 10 25 Q 15 15 20 10 Z' fill='${colors.leaf2}' opacity='${settings.opacity2}'/%3E%3C/svg%3E")
          `,
          backgroundSize: `${settings.size1} ${settings.size1}, ${settings.size2} ${settings.size2}`,
          backgroundPosition: '0 0, 150px 0'
        }}
      />

      {/* 背景レイヤー2 - 中サイズの花びら */}
      <div 
        className="falling-layer falling-layer-2"
        style={{
          backgroundImage: `
            url("data:image/svg+xml,%3Csvg width='50' height='50' viewBox='0 0 50 50' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M25 15 Q 30 20 25 25 Q 20 20 15 25 Q 20 20 25 15 Z' fill='${colors.leaf3}' opacity='${settings.opacity2}'/%3E%3C/svg%3E")
          `,
          backgroundSize: `180px 180px`,
          backgroundPosition: '200px 0'
        }}
      />

      {/* 背景レイヤー3 - 小さな桜の花びら */}
      <div 
        className="falling-layer falling-layer-3"
        style={{
          backgroundImage: `
            url("data:image/svg+xml,%3Csvg width='30' height='30' viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 8 Q 18 12 15 18 Q 12 14 8 18 Q 12 12 15 8 Z' fill='${colors.petal1}' opacity='${settings.opacity3}'/%3E%3C/svg%3E")
          `,
          backgroundSize: `${settings.size3} ${settings.size3}`,
          backgroundPosition: '80px 0'
        }}
      />

      {/* 背景レイヤー4 - 極小の花びら */}
      <div 
        className="falling-layer falling-layer-4"
        style={{
          backgroundImage: `
            url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 5 Q 13 8 10 12 Q 7 9 4 12 Q 7 8 10 5 Z' fill='${colors.petal2}' opacity='${settings.opacity3}'/%3E%3C/svg%3E")
          `,
          backgroundSize: `100px 100px`,
          backgroundPosition: '120px 0'
        }}
      />

      <style jsx>{`
        .falling-leaves-background {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          pointer-events: none;
          z-index: 0;
          overflow: hidden;
        }

        .falling-layer {
          position: absolute;
          top: -200px;
          left: 0;
          width: 100%;
          height: calc(100% + 400px);
          pointer-events: none;
        }

        .falling-layer-1 {
          animation: fallDown1 20s linear infinite;
        }

        .falling-layer-2 {
          top: -250px;
          height: calc(100% + 500px);
          animation: fallDown2 30s linear infinite;
        }

        .falling-layer-3 {
          top: -300px;
          height: calc(100% + 600px);
          animation: sakuraFall 25s linear infinite;
        }

        .falling-layer-4 {
          top: -350px;
          height: calc(100% + 700px);
          animation: petalFall 35s linear infinite;
        }

        @keyframes fallDown1 {
          0% { 
            transform: translateY(-200px) translateX(0px) rotate(0deg);
          }
          25% { 
            transform: translateY(25vh) translateX(15px) rotate(45deg);
          }
          50% { 
            transform: translateY(50vh) translateX(-8px) rotate(90deg);
          }
          75% { 
            transform: translateY(75vh) translateX(10px) rotate(135deg);
          }
          100% { 
            transform: translateY(100vh) translateX(0px) rotate(180deg);
          }
        }

        @keyframes fallDown2 {
          0% { 
            transform: translateY(-250px) translateX(0px) rotate(0deg);
          }
          20% { 
            transform: translateY(20vh) translateX(-10px) rotate(36deg);
          }
          40% { 
            transform: translateY(40vh) translateX(18px) rotate(72deg);
          }
          60% { 
            transform: translateY(60vh) translateX(-12px) rotate(108deg);
          }
          80% { 
            transform: translateY(80vh) translateX(8px) rotate(144deg);
          }
          100% { 
            transform: translateY(110vh) translateX(0px) rotate(180deg);
          }
        }

        @keyframes sakuraFall {
          0% { 
            transform: translateY(-300px) translateX(0px) rotate(0deg);
          }
          25% { 
            transform: translateY(25%) translateX(-20px) rotate(45deg);
          }
          50% { 
            transform: translateY(50%) translateX(15px) rotate(90deg);
          }
          75% { 
            transform: translateY(75%) translateX(-10px) rotate(135deg);
          }
          100% { 
            transform: translateY(120%) translateX(8px) rotate(180deg);
          }
        }

        @keyframes petalFall {
          0% { 
            transform: translateY(-400px) translateX(0px) rotate(0deg);
          }
          20% { 
            transform: translateY(20%) translateX(15px) rotate(36deg);
          }
          40% { 
            transform: translateY(40%) translateX(-12px) rotate(72deg);
          }
          60% { 
            transform: translateY(60%) translateX(20px) rotate(108deg);
          }
          80% { 
            transform: translateY(80%) translateX(-8px) rotate(144deg);
          }
          100% { 
            transform: translateY(130%) translateX(0px) rotate(180deg);
          }
        }

        /* アクセシビリティ対応 */
        @media (prefers-reduced-motion: reduce) {
          .falling-layer {
            animation: none;
          }
        }

        /* モバイル対応 */
        @media (max-width: 768px) {
          .falling-layer-1 {
            animation-duration: 25s;
          }
          .falling-layer-2 {
            animation-duration: 35s;
          }
          .falling-layer-3 {
            animation-duration: 30s;
          }
          .falling-layer-4 {
            animation-duration: 40s;
          }
        }
      `}</style>
    </div>
  );
}
