'use client';

import { useState } from 'react';
import Link from 'next/link';
import AspectRatioImage from '@/components/AspectRatioImage';
import Lightbox from '@/components/Lightbox';

interface WorkProps {
  id: string
  title: string
  description: string
  imageUrl: string
  category: string
  year: number
}

export default function WorkCard({ id, title, description, imageUrl, category, year }: WorkProps) {
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [clickPosition, setClickPosition] = useState<{ x: number; y: number } | undefined>();
  const [thumbnailRect, setThumbnailRect] = useState<DOMRect | undefined>();

  // カテゴリの日本語表示
  const getCategoryLabel = (category: string) => {
    const categoryMap: { [key: string]: string } = {
      'painting': '絵画',
      'sculpture': '彫刻',
      'photography': '写真',
      'digital': 'デジタル',
      'other': 'その他'
    };
    return categoryMap[category] || category;
  };

  const handleImageClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    setClickPosition({ x: e.clientX, y: e.clientY });
    setThumbnailRect(rect);
    setIsLightboxOpen(true);
  };

  return (
    <>
      <div className="bg-white rounded-lg shadow-md overflow-hidden art-card group">
        <div className="overflow-hidden relative cursor-pointer" onClick={handleImageClick}>
          <AspectRatioImage
            src={imageUrl}
            alt={title}
            containerClassName="rounded-t-lg art-card-image"
          />
          <div className="art-card-overlay"></div>
          {/* 拡大アイコン */}
          <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
            </svg>
          </div>
        </div>
      <div className="p-5">
        <div className="flex justify-between items-start mb-3">
          <h3 className="text-lg font-serif font-medium text-primary-800">{title}</h3>
          <span className="text-xs font-medium bg-primary-50 text-primary-700 px-2.5 py-1 rounded-full">
            {getCategoryLabel(category)}
          </span>
        </div>
        <p className="text-secondary-600 text-sm mb-3">{year}年</p>
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">{description}</p>
        <Link
          href={`/works/${id}`}
          className="text-primary-600 hover:text-primary-800 font-medium inline-flex items-center text-sm group"
        >
          詳細を見る
          <svg 
            className="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-300" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24" 
            xmlns="http://www.w3.org/2000/svg"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth="2" 
              d="M13 7l5 5m0 0l-5 5m5-5H6"
            ></path>
          </svg>
        </Link>
      </div>
    </div>

    {/* ライトボックス */}
    <Lightbox
      isOpen={isLightboxOpen}
      onClose={() => setIsLightboxOpen(false)}
      imageSrc={imageUrl}
      imageAlt={title}
      title={title}
      description={`${year}年 - ${description}`}
      clickPosition={clickPosition}
      thumbnailRect={thumbnailRect}
    />
  </>
  )
}
