'use client';

import { useState } from 'react';

export type CharacterType = 'bird' | 'owl' | 'hamster';

interface AnimatedCharacterProps {
  type: CharacterType;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export default function AnimatedCharacter({ 
  type, 
  size = 'md', 
  className = '' 
}: AnimatedCharacterProps) {
  const [paused, setPaused] = useState(false);

  const sizeClasses = {
    sm: 'w-16 h-12',
    md: 'w-24 h-18',
    lg: 'w-32 h-24'
  };

  const handleClick = () => {
    setPaused(p => !p);
  };

  const renderCharacter = () => {
    switch (type) {
      case 'bird':
        return <BirdSVG />;
      case 'owl':
        return <OwlSVG />;
      case 'hamster':
        return <HamsterSVG />;
      default:
        return <BirdSVG />;
    }
  };

  return (
    <div
      className={`character-inline character-${type} ${sizeClasses[size]} ${paused ? 'paused' : ''} ${className}`}
      onClick={handleClick}
      title={`${getCharacterName(type)}（クリックで一時停止/再生）`}
      role="img"
      aria-label={`${getCharacterName(type)}の装飾アニメーション`}
    >
      <div className="shadow anim" />
      {renderCharacter()}
    </div>
  );
}

function getCharacterName(type: CharacterType): string {
  switch (type) {
    case 'bird': return '羽ばたくインコ';
    case 'owl': return '可愛いふくろう';
    case 'hamster': return '可愛いハムスター';
    default: return 'キャラクター';
  }
}

// Bird SVG Component (existing)
function BirdSVG() {
  return (
    <svg viewBox="0 0 200 200" className="anim character-svg" aria-hidden="true">
      <g className="bird use-fill-box">
        <g className="wingR anim use-fill-box" style={{ transformOrigin: '112px 93px' }}>
          <path d="M112 93 C 96 88, 86 78, 78 66 C 96 64, 112 70, 128 84 C 120 92, 114 94, 112 93 Z" fill="var(--wing)" stroke="var(--stroke)" strokeWidth="2"/>
        </g>
        <g className="body use-fill-box" style={{ transformOrigin: '110px 112px' }}>
          <ellipse cx="110" cy="112" rx="34" ry="40" fill="var(--body)" stroke="var(--stroke)" strokeWidth="2"/>
          <ellipse cx="118" cy="126" rx="20" ry="18" fill="var(--belly)" opacity="0.9"/>
        </g>
        <g className="tail anim use-fill-box" style={{ transformOrigin: '92px 140px' }}>
          <path d="M92 140 L70 166 C76 168, 90 168, 104 164 Z" fill="var(--wing)" stroke="var(--stroke)" strokeWidth="2"/>
        </g>
        <g className="wingL anim use-fill-box" style={{ transformOrigin: '112px 93px' }}>
          <path d="M112 93 C 98 90, 84 84, 74 74 C 94 70, 116 74, 136 88 C 126 94, 116 96, 112 93 Z" fill="var(--wing)" stroke="var(--stroke)" strokeWidth="2"/>
        </g>
        <g className="head use-fill-box" style={{ transformOrigin: '130px 92px' }}>
          <circle cx="132" cy="92" r="18" fill="var(--body)" stroke="var(--stroke)" strokeWidth="2"/>
          <circle cx="124" cy="96" r="6" fill="var(--cheek)"/>
          <g className="eye anim use-fill-box" style={{ transformOrigin: '138px 90px' }}>
            <ellipse cx="138" cy="90" rx="3.8" ry="3.8" fill="var(--eye)"/>
          </g>
          <path d="M148 96 L160 101 L148 106 Z" fill="var(--beak)" stroke="var(--stroke)" strokeWidth="2"/>
        </g>
        <g className="use-fill-box" style={{ transformOrigin: '118px 144px' }}>
          <path d="M116 144 q-4 8 4 8" fill="none" stroke="var(--stroke)" strokeWidth="2"/>
          <path d="M124 144 q-2 8 6 8" fill="none" stroke="var(--stroke)" strokeWidth="2"/>
        </g>
      </g>
    </svg>
  );
}

// Owl SVG Component (converted from HTML)
function OwlSVG() {
  return (
    <svg viewBox="0 0 300 350" className="anim character-svg" aria-hidden="true">
      {/* 影 */}
      <ellipse cx="150" cy="320" rx="60" ry="15" fill="rgba(0,0,0,0.2)">
        <animateTransform 
          attributeName="transform" 
          type="scale" 
          values="1,1;1.1,1;1,1" 
          dur="3s" 
          repeatCount="indefinite"/>
      </ellipse>
      
      {/* 足 */}
      <g id="feet">
        <g transform="translate(120, 280)">
          <ellipse cx="0" cy="0" rx="12" ry="8" fill="#d2691e">
            <animateTransform 
              attributeName="transform" 
              type="rotate" 
              values="0;-10;5;0" 
              dur="2s" 
              repeatCount="indefinite"/>
          </ellipse>
          <path d="M-8,-3 Q-12,-8 -10,-5 M0,-5 Q0,-10 2,-7 M8,-3 Q12,-8 10,-5" 
                stroke="#b8560f" strokeWidth="2" fill="none"/>
        </g>
        
        <g transform="translate(180, 285)">
          <ellipse cx="0" cy="0" rx="12" ry="8" fill="#d2691e">
            <animateTransform 
              attributeName="transform" 
              type="rotate" 
              values="0;10;-5;0" 
              dur="2.5s" 
              repeatCount="indefinite"/>
          </ellipse>
          <path d="M-8,-3 Q-12,-8 -10,-5 M0,-5 Q0,-10 2,-7 M8,-3 Q12,-8 10,-5" 
                stroke="#b8560f" strokeWidth="2" fill="none"/>
        </g>
      </g>
      
      {/* 体 */}
      <g id="body">
        <ellipse cx="150" cy="200" rx="80" ry="90" fill="#cd853f" stroke="#8b4513" strokeWidth="4"/>
        <ellipse cx="150" cy="220" rx="50" ry="60" fill="#f5deb3"/>
        <g fill="#cd853f">
          <path d="M 130 200 Q 140 205 150 200 Q 160 205 170 200" stroke="#cd853f" strokeWidth="2" fill="none"/>
          <path d="M 135 220 Q 145 225 155 220" stroke="#cd853f" strokeWidth="2" fill="none"/>
          <path d="M 125 240 Q 135 245 145 240" stroke="#cd853f" strokeWidth="2" fill="none"/>
          <path d="M 155 240 Q 165 245 175 240" stroke="#cd853f" strokeWidth="2" fill="none"/>
          <path d="M 140 260 Q 150 265 160 260" stroke="#cd853f" strokeWidth="2" fill="none"/>
        </g>
      </g>
      
      {/* 頭（アニメーション付き） */}
      <g id="head">
        <animateTransform 
          attributeName="transform" 
          type="rotate" 
          values="0 150 120;-8 150 120;5 150 120;0 150 120;3 150 120;-5 150 120;0 150 120" 
          dur="6s" 
          repeatCount="indefinite"/>
        
        <circle cx="150" cy="120" r="70" fill="#cd853f" stroke="#8b4513" strokeWidth="4"/>
        
        {/* 耳 */}
        <g id="ears">
          <path d="M 100 80 Q 90 60 110 70 Q 120 75 115 85 Z" fill="#cd853f" stroke="#8b4513" strokeWidth="3"/>
          <path d="M 105 75 Q 100 65 110 70 Q 115 72 112 78 Z" fill="#deb887"/>
          <path d="M 200 80 Q 210 60 190 70 Q 180 75 185 85 Z" fill="#cd853f" stroke="#8b4513" strokeWidth="3"/>
          <path d="M 195 75 Q 200 65 190 70 Q 185 72 188 78 Z" fill="#deb887"/>
        </g>
        
        {/* 目 */}
        <g id="eyes">
          <circle cx="125" cy="110" r="22" fill="#f5f5dc" stroke="#8b4513" strokeWidth="2"/>
          <circle cx="175" cy="110" r="22" fill="#f5f5dc" stroke="#8b4513" strokeWidth="2"/>
          
          <g id="pupils">
            <circle cx="125" cy="110" r="12" fill="#2f1b14">
              <animate attributeName="cy" values="110;108;110" dur="4s" repeatCount="indefinite"/>
            </circle>
            <circle cx="175" cy="110" r="12" fill="#2f1b14">
              <animate attributeName="cy" values="110;108;110" dur="4s" repeatCount="indefinite"/>
            </circle>
            <circle cx="130" cy="105" r="4" fill="white"/>
            <circle cx="180" cy="105" r="4" fill="white"/>
          </g>
          
          {/* まばたき */}
          <g id="blink">
            <ellipse cx="125" cy="110" rx="22" ry="0" fill="#cd853f" opacity="0">
              <animate attributeName="ry" values="0;22;0" dur="0.3s" begin="0s;5s;8s;12s" fill="freeze"/>
              <animate attributeName="opacity" values="0;1;0" dur="0.3s" begin="0s;5s;8s;12s" fill="freeze"/>
            </ellipse>
            <ellipse cx="175" cy="110" rx="22" ry="0" fill="#cd853f" opacity="0">
              <animate attributeName="ry" values="0;22;0" dur="0.3s" begin="0s;5s;8s;12s" fill="freeze"/>
              <animate attributeName="opacity" values="0;1;0" dur="0.3s" begin="0s;5s;8s;12s" fill="freeze"/>
            </ellipse>
          </g>
        </g>
        
        {/* くちばし */}
        <g id="beak">
          <path d="M 150 130 L 145 140 L 155 140 Z" fill="#ff8c00" stroke="#d2691e" strokeWidth="2">
            <animateTransform 
              attributeName="transform" 
              type="scale" 
              values="1;1.1;1" 
              dur="3s" 
              repeatCount="indefinite"/>
          </path>
        </g>
      </g>
      
      {/* 羽ばたくような微細な動き */}
      <g id="wings" opacity="0.3">
        <ellipse cx="100" cy="180" rx="15" ry="40" fill="#8b4513">
          <animateTransform 
            attributeName="transform" 
            type="rotate" 
            values="0 100 180;-15 100 180;0 100 180" 
            dur="4s" 
            repeatCount="indefinite"/>
        </ellipse>
        <ellipse cx="200" cy="180" rx="15" ry="40" fill="#8b4513">
          <animateTransform 
            attributeName="transform" 
            type="rotate" 
            values="0 200 180;15 200 180;0 200 180" 
            dur="4s" 
            repeatCount="indefinite"/>
        </ellipse>
      </g>
    </svg>
  );
}

// Hamster SVG Component (converted from HTML)
function HamsterSVG() {
  return (
    <svg viewBox="0 0 280 320" className="anim character-svg" aria-hidden="true">
      {/* 影 */}
      <ellipse cx="140" cy="300" rx="45" ry="12" fill="rgba(0,0,0,0.2)">
        <animateTransform
          attributeName="transform"
          type="scale"
          values="1,1;1.05,1;1,1"
          dur="4s"
          repeatCount="indefinite"/>
      </ellipse>

      {/* 足 */}
      <g id="feet">
        <ellipse cx="115" cy="285" rx="8" ry="6" fill="#c49764" stroke="#a0814d" strokeWidth="2">
          <animateTransform
            attributeName="transform"
            type="rotate"
            values="0 115 285;-5 115 285;5 115 285;0 115 285"
            dur="2.5s"
            repeatCount="indefinite"/>
        </ellipse>

        <ellipse cx="165" cy="285" rx="8" ry="6" fill="#c49764" stroke="#a0814d" strokeWidth="2">
          <animateTransform
            attributeName="transform"
            type="rotate"
            values="0 165 285;5 165 285;-5 165 285;0 165 285"
            dur="2.8s"
            repeatCount="indefinite"/>
        </ellipse>

        <ellipse cx="105" cy="255" rx="6" ry="8" fill="#c49764" stroke="#a0814d" strokeWidth="2">
          <animateTransform
            attributeName="transform"
            type="translate"
            values="0,0;-2,1;1,-1;0,0"
            dur="1.8s"
            repeatCount="indefinite"/>
        </ellipse>

        <ellipse cx="175" cy="255" rx="6" ry="8" fill="#c49764" stroke="#a0814d" strokeWidth="2">
          <animateTransform
            attributeName="transform"
            type="translate"
            values="0,0;2,1;-1,-1;0,0"
            dur="2.1s"
            repeatCount="indefinite"/>
        </ellipse>
      </g>

      {/* 体 */}
      <g id="body">
        <ellipse cx="140" cy="220" rx="50" ry="65" fill="#ddb892" stroke="#a0814d" strokeWidth="3"/>

        {/* 手 */}
        <g id="hands" fill="#c49764" stroke="#a0814d" strokeWidth="2">
          <g transform="translate(110, 200)">
            <circle cx="0" cy="0" r="8">
              <animateTransform
                attributeName="transform"
                type="rotate"
                values="0;-15;10;0"
                dur="3s"
                repeatCount="indefinite"/>
            </circle>
            <circle cx="-3" cy="-5" r="2" fill="#ddb892"/>
            <circle cx="3" cy="-5" r="2" fill="#ddb892"/>
          </g>

          <g transform="translate(170, 200)">
            <circle cx="0" cy="0" r="8">
              <animateTransform
                attributeName="transform"
                type="rotate"
                values="0;15;-10;0"
                dur="3.5s"
                repeatCount="indefinite"/>
            </circle>
            <circle cx="-3" cy="-5" r="2" fill="#ddb892"/>
            <circle cx="3" cy="-5" r="2" fill="#ddb892"/>
          </g>
        </g>
      </g>

      {/* 頭 */}
      <g id="head">
        <animateTransform
          attributeName="transform"
          type="rotate"
          values="0 140 140;-3 140 140;2 140 140;0 140 140;1 140 140;-2 140 140;0 140 140"
          dur="5s"
          repeatCount="indefinite"/>

        <ellipse cx="140" cy="140" rx="45" ry="42" fill="#ddb892" stroke="#a0814d" strokeWidth="3"/>

        {/* 耳 */}
        <g id="ears">
          <circle cx="115" cy="115" r="18" fill="#ddb892" stroke="#a0814d" strokeWidth="3">
            <animateTransform
              attributeName="transform"
              type="rotate"
              values="0 115 115;-5 115 115;5 115 115;0 115 115"
              dur="4s"
              repeatCount="indefinite"/>
          </circle>
          <circle cx="115" cy="115" r="10" fill="#ddb892"/>
          <ellipse cx="118" cy="112" rx="3" ry="2" fill="#c49764"/>

          <circle cx="165" cy="115" r="18" fill="#ddb892" stroke="#a0814d" strokeWidth="3">
            <animateTransform
              attributeName="transform"
              type="rotate"
              values="0 165 115;5 165 115;-5 165 115;0 165 115"
              dur="4.2s"
              repeatCount="indefinite"/>
          </circle>
          <circle cx="165" cy="115" r="10" fill="#ddb892"/>
          <ellipse cx="162" cy="112" rx="3" ry="2" fill="#c49764"/>
        </g>

        {/* 目 */}
        <g id="eyes">
          <circle cx="125" cy="135" r="8" fill="#2d1810">
            <animate attributeName="cy" values="135;133;135" dur="3s" repeatCount="indefinite"/>
          </circle>
          <circle cx="127" cy="132" r="2" fill="white"/>

          <circle cx="155" cy="135" r="8" fill="#2d1810">
            <animate attributeName="cy" values="135;133;135" dur="3s" repeatCount="indefinite"/>
          </circle>
          <circle cx="157" cy="132" r="2" fill="white"/>

          {/* まばたき */}
          <g id="blink">
            <ellipse cx="125" cy="135" rx="8" ry="0" fill="#ddb892" opacity="0">
              <animate attributeName="ry" values="0;8;0" dur="0.2s" begin="0s;4s;7s;11s" fill="freeze"/>
              <animate attributeName="opacity" values="0;1;0" dur="0.2s" begin="0s;4s;7s;11s" fill="freeze"/>
            </ellipse>
            <ellipse cx="155" cy="135" rx="8" ry="0" fill="#ddb892" opacity="0">
              <animate attributeName="ry" values="0;8;0" dur="0.2s" begin="0s;4s;7s;11s" fill="freeze"/>
              <animate attributeName="opacity" values="0;1;0" dur="0.2s" begin="0s;4s;7s;11s" fill="freeze"/>
            </ellipse>
          </g>
        </g>

        {/* 鼻 */}
        <ellipse cx="140" cy="150" rx="2" ry="3" fill="#a0814d">
          <animateTransform
            attributeName="transform"
            type="scale"
            values="1;1.2;1"
            dur="2s"
            repeatCount="indefinite"/>
        </ellipse>

        {/* 口 */}
        <g id="mouth" fill="none" stroke="#a0814d" strokeWidth="2" strokeLinecap="round">
          <path d="M 140 155 Q 135 160 130 155"/>
          <path d="M 140 155 Q 145 160 150 155"/>
        </g>

        {/* ひげ */}
        <g id="whiskers" stroke="#a0814d" strokeWidth="2" fill="none" strokeLinecap="round">
          <line x1="105" y1="145" x2="85" y2="140">
            <animateTransform
              attributeName="transform"
              type="rotate"
              values="0 105 145;-2 105 145;2 105 145;0 105 145"
              dur="3s"
              repeatCount="indefinite"/>
          </line>
          <line x1="105" y1="155" x2="85" y2="155">
            <animateTransform
              attributeName="transform"
              type="rotate"
              values="0 105 155;2 105 155;-2 105 155;0 105 155"
              dur="3.5s"
              repeatCount="indefinite"/>
          </line>

          <line x1="175" y1="145" x2="195" y2="140">
            <animateTransform
              attributeName="transform"
              type="rotate"
              values="0 175 145;2 175 145;-2 175 145;0 175 145"
              dur="3s"
              repeatCount="indefinite"/>
          </line>
          <line x1="175" y1="155" x2="195" y2="155">
            <animateTransform
              attributeName="transform"
              type="rotate"
              values="0 175 155;-2 175 155;2 175 155;0 175 155"
              dur="3.5s"
              repeatCount="indefinite"/>
          </line>
        </g>
      </g>

      {/* 頬の丸み */}
      <g id="cheeks" opacity="0.6">
        <circle cx="100" cy="150" r="12" fill="#ddb892">
          <animateTransform
            attributeName="transform"
            type="scale"
            values="1;1.1;1"
            dur="4s"
            repeatCount="indefinite"/>
        </circle>
        <circle cx="180" cy="150" r="12" fill="#ddb892">
          <animateTransform
            attributeName="transform"
            type="scale"
            values="1;1.1;1"
            dur="4.2s"
            repeatCount="indefinite"/>
        </circle>
      </g>
    </svg>
  );
}
