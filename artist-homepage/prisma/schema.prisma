datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl-arm64-openssl-3.0.x"]
}

model BlogPost {
  id             Int      @id @default(autoincrement())
  title          String   // タイトル
  content        String   @db.Text  // 内容（長文対応）
  image_data     String?  @db.Text  // 画像データ
  thumbnail_data String?  @db.Text  // サムネイル画像データ
  url            String?  // 外部リンクURL
  posted_at      DateTime // 投稿日
  created_at     DateTime @default(now())
  updated_at     DateTime @updatedAt
  is_active      Boolean  @default(true) // 公開/非公開フラグ
  is_featured    <PERSON><PERSON><PERSON>  @default(false) // トップページ表示フラグ

  @@map("blogpost")
}

model User {
  id         Int      @id @default(autoincrement())
  name       String
  email      String   @unique
  password   String
  created_at DateTime @default(now())

  @@map("users")
}

model SocialMediaSetting {
  id               Int      @id @default(autoincrement())
  platform         String
  url              String
  icon_name        String
  is_active        Boolean  @default(true)
  order_index      Int
  profile_image    String?  @db.Text  // プロフィール画像
  artist_bio       String?  @db.Text  // アーティスト紹介
  awards           String?  @db.Text  // 受賞歴
  created_at       DateTime @default(now())
  updated_at       DateTime @updatedAt

  @@map("social_media_settings")
}

model hero_slides {
  id          Int      @id @default(autoincrement())
  title       String
  description String?
  image_url   String
  link_url    String?
  order_index Int
  is_active   Boolean  @default(true)
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt
}

model works {
  id             Int      @id @default(autoincrement())
  title          String
  description    String?
  image_data     String
  thumbnail_data String
  category       String
  tags           String[]
  dimensions     String?
  materials      String?
  year           Int
  is_featured    Boolean  @default(false)
  order_index    Int      @default(0)
  url_link       String?  // 作品関連のURL（外部リンク）
  created_at     DateTime @default(now())
  updated_at     DateTime @updatedAt
}

model posts {
  id           Int       @id @default(autoincrement())
  title        String
  content      String
  excerpt      String?
  image_data   String?
  type         String
  tags         String[]
  is_published Boolean   @default(false)
  published_at DateTime?
  created_at   DateTime  @default(now())
  updated_at   DateTime  @updatedAt
}

model ContactMessage {
  id         Int      @id @default(autoincrement())
  name       String
  email      String
  message    String   @db.Text
  is_read    Boolean  @default(false)
  created_at DateTime @default(now())

  @@map("contact_messages")
}

model StudioItem {
  id          Int      @id @default(autoincrement())
  title       String
  description String   @db.Text
  image_data  String   @db.Text
  order_index Int
  is_active   Boolean  @default(true)
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  @@map("studio_items")
}